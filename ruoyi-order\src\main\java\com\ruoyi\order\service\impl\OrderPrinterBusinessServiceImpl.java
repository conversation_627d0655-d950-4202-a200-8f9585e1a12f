package com.ruoyi.order.service.impl;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.oss.ALY_OSS;
import com.ruoyi.order.domain.OrderPrinter;
import com.ruoyi.order.domain.OrderPrinterTask;
import com.ruoyi.order.dto.UploadFileRequest;
import com.ruoyi.order.service.IOrderPrinterBusinessService;
import com.ruoyi.order.service.IOrderPrinterService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.hutool.extra.servlet.ServletUtil.getClientIP;

/**
 * 订单打印机业务服务实现类
 * 处理复杂的业务逻辑，包括文件上传、用户认证、参数验证等
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class OrderPrinterBusinessServiceImpl implements IOrderPrinterBusinessService {
    
    private static final Logger log = LoggerFactory.getLogger(OrderPrinterBusinessServiceImpl.class);
    
    @Autowired
    private IOrderPrinterService orderPrinterService;
    

    

    
    @Override
    public Map<String, Object> uploadAndCreateOrder(
            MultipartFile file,
            MultipartFile[] files,
            String deviceId,
            String deviceName,
            String openid,
            Integer copies,
            Integer colorMode,
            Integer duplexMode,
            Integer paperType,
            String pageRange) {
        
        // 参数验证
        String validationError = validateBasicParams(deviceId, file, files);
        if (validationError != null) {
            throw new RuntimeException(validationError);
        }
        
        // 如果openid为空，生成访客openid
        if (StringUtils.isEmpty(openid)) {
            openid = "guest_" + deviceId + "_" + System.currentTimeMillis();
            log.info("生成访客openid: {}", openid);
        }
        
        // 创建订单
        Map<String, Object> orderResult = createOrder(deviceId, deviceName, openid, null);
        String orderId = (String) orderResult.get("orderId");
        
        // 上传文件
        Map<String, Object> uploadResult = uploadFile(orderId, file, copies, colorMode, duplexMode, paperType, pageRange);
        
        // 合并结果
        Map<String, Object> result = new HashMap<>();
        result.putAll(orderResult);
        result.putAll(uploadResult);
        
        return result;
    }
    
    @Override
    public Map<String, Object> createOrder(String deviceId, String deviceName, String openid, String phone) {
        // 如果openid为空，生成访客openid
        if (StringUtils.isEmpty(openid)) {
            openid = "guest_" + deviceId + "_" + System.currentTimeMillis();
            log.info("生成访客openid: {}", openid);
        }

        // 创建订单参数
        Map<String, Object> params = new HashMap<>();
        params.put("deviceId", deviceId);
        params.put("deviceName", deviceName);
        params.put("openid", openid);
        params.put("phone", phone);

        // 调用服务层创建订单
        return orderPrinterService.createOrder(params);
    }
    
    @Override
    public Map<String, Object> uploadFile(
            String orderId,
            MultipartFile file,
            Integer copies,
            Integer colorMode,
            Integer duplexMode,
            Integer paperType,
            String pageRange) {
        
        // 参数验证
        String validationError = validateOrderParams(orderId, file);
        if (validationError != null) {
            throw new RuntimeException(validationError);
        }
        
        try {
            // 上传文件到阿里云OSS
            String originalFilename = file.getOriginalFilename();
            String fileExtension = StringUtils.substringAfterLast(originalFilename, ".");
            String fileName = "printer_files/" + orderId + "/" + System.currentTimeMillis() + "." + fileExtension;

            log.info("开始上传文件到阿里云OSS: {}", fileName);
            String url = ALY_OSS.uploadImage(file, fileName);
            // 移除URL中的过期时间参数，获取永久访问地址
            url = url.substring(0, url.indexOf("?"));
            log.info("文件上传成功 - 文件名: {}, URL: {}", fileName, url);
            
            // 准备文件参数
            Map<String, Object> fileParams = new HashMap<>();
            fileParams.put("fileUrl", url);
            fileParams.put("fileName", fileName); // OSS中的文件路径
            fileParams.put("originalFileName", file.getOriginalFilename()); // 用户上传的原始文件名
            fileParams.put("fileType", StringUtils.substringAfterLast(file.getOriginalFilename(), "."));
            fileParams.put("fileSize", file.getSize());
            fileParams.put("copies", copies);
            fileParams.put("colorMode", colorMode);
            fileParams.put("duplexMode", duplexMode);
            fileParams.put("paperType", paperType);
            fileParams.put("pageRange", pageRange);
            
            // 上传文件并计算价格
            return orderPrinterService.uploadFileAndCalculatePrice(orderId, fileParams);
        } catch (Exception e) {
            log.error("上传文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("上传文件失败: " + e.getMessage());
        }
    }
    
    @Override
    public String validateBasicParams(String deviceId, MultipartFile file, MultipartFile[] files) {
        if (StringUtils.isEmpty(deviceId)) {
            return "设备ID不能为空";
        }
        
        if ((file == null || file.isEmpty()) && (files == null || files.length == 0)) {
            return "请上传文件";
        }
        
        return null; // 验证通过
    }
    
    @Override
    public String validateOrderParams(String orderId, MultipartFile file) {
        if (StringUtils.isEmpty(orderId)) {
            return "订单ID不能为空";
        }
        
        if (file == null || file.isEmpty()) {
            return "请上传文件";
        }
        
        return null; // 验证通过
    }
    
    @Override
    public void logRequestInfo(HttpServletRequest request, String methodName, Map<String, Object> params) {
        log.info("=== 开始处理{}请求 ===", methodName);
        log.info("请求IP: {}", getClientIP(request));
        log.info("请求参数: {}", params);
    }
    
    @Override
    public void logFileInfo(MultipartFile file, MultipartFile[] files) {
        if (file != null && !file.isEmpty()) {
            log.info("文件信息 - 文件名: {}, 大小: {} bytes, 类型: {}",
                    file.getOriginalFilename(), file.getSize(), file.getContentType());
        }
        
        if (files != null && files.length > 0) {
            for (int i = 0; i < files.length; i++) {
                MultipartFile f = files[i];
                if (f != null && !f.isEmpty()) {
                    log.info("文件{}信息 - 文件名: {}, 大小: {} bytes, 类型: {}",
                            i + 1, f.getOriginalFilename(), f.getSize(), f.getContentType());
                }
            }
        }
    }
    


    @Override
    public Map<String, Object> getOrderDetail(String orderId, String openid) {
        if (StringUtils.isEmpty(orderId)) {
            throw new RuntimeException("订单ID不能为空");
        }

        // 获取订单信息
        OrderPrinter order = orderPrinterService.selectOrderPrinterByOrderId(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }

        // 如果提供了openid，验证订单是否属于该用户
        if (StringUtils.isNotEmpty(openid) && !openid.equals(order.getOpenid())) {
            throw new RuntimeException("无权查看该订单");
        }

        // 获取订单关联的打印任务
        List<OrderPrinterTask> tasks = orderPrinterService.getOrderTasks(orderId);

        // 计算订单总金额
        Double totalAmount = orderPrinterService.calculateOrderTotalAmount(orderId);
        order.setTotalAmount(totalAmount != null ? Math.round(totalAmount * 100) : 0L); // 转换为分

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("order", order);
        result.put("tasks", tasks);
        result.put("totalAmount", totalAmount); // 额外返回总金额字段

        return result;
    }
}
