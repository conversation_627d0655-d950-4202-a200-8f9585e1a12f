-- 为订单打印机表添加总金额字段
-- 执行时间：2025-01-29

-- 添加总金额字段（单位：分）
ALTER TABLE `order_printer` 
ADD COLUMN `total_amount` BIGINT(20) NULL DEFAULT 0 COMMENT '订单总金额（所有任务价格之和）单位：分' 
AFTER `print_time`;

-- 更新现有订单的总金额（基于任务价格计算）
UPDATE `order_printer` op 
SET `total_amount` = (
    SELECT COALESCE(SUM(ROUND(opt.task_price * 100)), 0)
    FROM `order_printer_tasks` opt 
    WHERE opt.order_id = op.order_id 
    AND opt.status = 0
)
WHERE op.total_amount IS NULL OR op.total_amount = 0;

-- 验证更新结果
SELECT 
    order_id,
    device_name,
    total_amount,
    (SELECT COUNT(*) FROM order_printer_tasks WHERE order_id = op.order_id) as task_count,
    (SELECT SUM(task_price) FROM order_printer_tasks WHERE order_id = op.order_id) as task_total_yuan
FROM order_printer op 
WHERE total_amount > 0 
LIMIT 10;
