package com.ruoyi.web.controller.wxapp.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.config.MyConfig;
import com.ruoyi.device.domain.DevicePrinter;
import com.ruoyi.device.service.IDevicePrinterService;
import com.ruoyi.enums.ChannelTypeEnum;
import com.ruoyi.enums.FunctionEnum;
import com.ruoyi.fee.domain.Fee;
import com.ruoyi.fee.service.IFeeService;
import com.ruoyi.order.domain.OrderPrinter;
import com.ruoyi.order.domain.OrderCollect;
import com.ruoyi.order.service.IOrderPrinterService;
import com.ruoyi.order.service.IOrderCollectService;
import com.ruoyi.po.JsPay;
import com.ruoyi.po.SplitBunch;
import com.ruoyi.po.SplitInfo;
import com.ruoyi.socket.WebSocketServer;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.utils.HttpsMain;
import com.ruoyi.utils.RsaUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

import static com.ruoyi.common.constant.OrderConstants.*;

/**
 * 打印机支付控制器
 * 模拟HLPay.java的支付功能，但针对打印机订单
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Slf4j
@Controller
@RequestMapping("/client/wxapp/printer/pay")
public class CPrinterPayController {
    
    @Autowired
    private IOrderPrinterService orderPrinterService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private IOrderCollectService orderCollectService;
    @Autowired
    private IDevicePrinterService devicePrinterService;
    @Autowired
    private IFeeService feeService;

    /**
     * 汇联获取openid才能创建订单
     */
    @GetMapping("/getOpenId")
    public Object getOpenId(HttpServletRequest request) throws UnsupportedEncodingException {

        String attach = request.getParameter("attach");
        String userInfo = request.getParameter("userInfo");
        JSONObject jsonObject = JSONObject.parseObject(userInfo);

        System.out.println(userInfo);

        String openId = null;
        if (jsonObject != null) {
            openId = jsonObject.getString("openid");
        }

        if (attach == null || openId == null) return null;
        OrderPrinter orderPrinter = orderPrinterService.selectOrderPrinterByOrderId(attach);

        WebSocketServer.sendInfo("smcg", orderPrinter.getDeviceId()); // 主推信息->扫码成功

        SysUser sysUser = userService.selectUserById(orderPrinter.getUserId());
        if (sysUser.getDeptId() != 201 && !sysUser.getDept().getAncestors().contains("201")) {
            // TODO: 一拍即合 云创 支付http://ypjh.cameraon.store
            String url = "http://ypjh.cameraon.store/pay";
            url += "?totalAmount=" + (orderPrinter.getTotalAmount() != null ? orderPrinter.getTotalAmount().intValue() : 0);
            url += "&openId=" + openId;
            url += "&orderId=" + attach;
            url += "&photoTypeName=" + URLEncoder.encode("打印服务","UTF-8") ;
            url += "&mchid=" + orderPrinter.getMchid();

            // 重定向到前端支付页面
            return "redirect:" + url;
        }

        ModelAndView modelAndView = null;

        if (orderPrinter.getMchid().equals("1398949712874")) //乐高的商户号有定制的支付页面
            modelAndView = new ModelAndView("jsPayLego");
        else
            modelAndView = new ModelAndView("jsPay");

        modelAndView.addObject("totalAmount", orderPrinter.getTotalAmount() != null ? orderPrinter.getTotalAmount().intValue() : 0);
        modelAndView.addObject("openId", openId);
        modelAndView.addObject("orderId", attach);
        modelAndView.addObject("mchid", orderPrinter.getMchid());

        return modelAndView;
    }

    /**
     * 发起JS支付
     *
     * @param totalAmount
     * @return
     * @throws IOException
     */
    @ResponseBody
    @PostMapping("/jsPay")
    public String doJsPay(int totalAmount, String openId, String outTradeNo, String mchid) throws Exception {

        OrderPrinter orderPrinter = orderPrinterService.selectOrderPrinterByOrderId(outTradeNo);
        if (orderPrinter == null) {
            return "订单号错误";
        }

        SysUser user = userService.selectUserById(orderPrinter.getUserId());
        if (user == null) {
            return "用户绑定错误";
        }

        boolean isPDL = false;
        String ancestors = user.getDept().getAncestors();
        Long deptId = user.getDept().getDeptId();
        if (ancestors.contains("201") || deptId == 201) {
            isPDL = true;
        }

        if (orderPrinter.getOrderStatus() != 0) {
            return "订单状态错误" + orderPrinter.getOrderStatus();
        }

        JsPay jsPay = new JsPay();
        jsPay.setOutTradeNo(outTradeNo);
        jsPay.setWxAppId(MyConfig.WX_APPID);
        jsPay.setBody("打印付款");
        jsPay.setHlMerchantId(mchid);
        /**去掉小数点后两位*/
        jsPay.setTotalAmount(String.valueOf(totalAmount));

        // 只支持微信支付
        if (openId == null || openId.equals("") || openId.equals("null")) {
            return "缺少微信openId";
        }
        jsPay.setOpenId(openId);
        orderPrinter.setOpenid(openId);
        jsPay.setChannelType(ChannelTypeEnum.WX.getCode());

        jsPay.setNotifyUrl(MyConfig.Notice_URL);   //回调地址
        jsPay.setSucUrl("https://fotobox.cameraon.store/digitalPhotoTongNiu?orderId=" + outTradeNo);  //支付成功跳转页面 todo 需动态设置

        DevicePrinter device = devicePrinterService.selectDevicePrinterByDeviceId(orderPrinter.getDeviceId());

        if (device == null)
            return "设备绑定异常";

        String subAccount = device.getSubAccount();
        // 打印机暂时不收取特殊费用，可以根据需要扩展
        Fee printFee = null; // feeService.query().eq("print_type", "PRINTER").one();

        if (subAccount != null && !subAccount.equals("")) {// 正常分账

            String[] FZitem = subAccount.split(",");
            int length = FZitem.length;

            //新分账逻辑//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
            String[] peopleMchids = new String[length];
            int[] peopleRates = new int[length];

            for (int i = 0; i < length; i++) {
                String[] people = FZitem[i].split(":");
                peopleMchids[i] = people[0];                       //商户号列表
                peopleRates[i] = Integer.parseInt(people[1]);      //分账比例列表
            }
            int[] moneys = splitInt(totalAmount, peopleRates);     //分钱列表

            Map<String, Integer> peopleMoneyMap = new HashMap<>();
            for (int i = 0; i < length; i++) {
                peopleMoneyMap.put(peopleMchids[i], moneys[i]);      // key：商户    value：分账金额
            }

            //如果特定类型收取手续费
            int fee = 0;
            if (printFee != null && (printFee.getExcludeDevice() == null || !printFee.getExcludeDevice().contains(orderPrinter.getDeviceId()))) {
                Integer chargeType = printFee.getChargeType();

                if (chargeType == 1) { //金额
                    fee = printFee.getFeeAmount();
                } else {               //比例
                    float feeRate = printFee.getFeeRate();
                    fee = (int) (totalAmount * feeRate);
                }
            }
            int i = 0;
            if (fee > 0) {
                if (peopleMoneyMap.containsKey(YC_MCHID)) {
                    peopleMoneyMap.put(YC_MCHID, peopleMoneyMap.get(YC_MCHID) + fee);
                } else if (peopleMoneyMap.containsKey(PDL_YC_MCHID)) {
                    peopleMoneyMap.put(PDL_YC_MCHID, peopleMoneyMap.get(PDL_YC_MCHID) + fee);
                } else {
                    peopleMoneyMap.put(!isPDL ? YC_MCHID : PDL_YC_MCHID, fee);
                }
                peopleMoneyMap.put(mchid, peopleMoneyMap.get(mchid) - fee);
                createInterfaceFeeOrder(orderPrinter, !isPDL ? YC_MCHID : PDL_YC_MCHID, fee, i++);
            }

            List<SplitInfo> acctInfos = new ArrayList<>();

            for (Map.Entry<String, Integer> entry : peopleMoneyMap.entrySet()) {
                System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                acctInfos.add(new SplitInfo(String.valueOf(entry.getValue()), entry.getKey()));

                if (entry.getKey().equals(mchid)) {              //订单主人
                    orderPrinter.setMoneyReceived(Long.valueOf(entry.getValue()));     //实收
                    orderPrinter.setInterfaceFee((long) fee);//接口费
                    orderPrinter.setCommission((long) Math.round(totalAmount * 0.006f));//手续费
                    orderPrinter.setAccount(orderPrinter.getMoneyReceived() - orderPrinter.getInterfaceFee() - orderPrinter.getCommission());  //到账
                    orderPrinterService.updateOrderPrinter(orderPrinter);
                } else {
                    createFZOrder(orderPrinter, entry.getKey(), entry.getValue() - fee, i++);
                }
            }

            //新分账逻辑///////////////////////////////////////////////////////////////

            SplitBunch splitBunch = new SplitBunch(String.valueOf(length), mchid, acctInfos);
            String jsonString = JSON.toJSONString(splitBunch);
            String encode = Base64.getEncoder().encodeToString(jsonString.getBytes());
            jsPay.setSplitBunch(encode);
        } else { //不分账
            if (printFee != null && (printFee.getExcludeDevice() == null || !printFee.getExcludeDevice().equals("all") || !printFee.getExcludeDevice().contains(orderPrinter.getDeviceId()))) {  //特定类型收取手续费
                Integer chargeType = printFee.getChargeType();
                int fee;
                if (chargeType == 1) { //金额
                    fee = printFee.getFeeAmount();
                } else {                    //比例
                    float feeRate = printFee.getFeeRate();
                    fee = (int) (totalAmount * feeRate);
                }
                List<SplitInfo> acctInfos = new ArrayList<>();
                acctInfos.add(new SplitInfo(String.valueOf(totalAmount - fee), mchid));
                acctInfos.add(new SplitInfo(String.valueOf(fee), !isPDL ? YC_MCHID : PDL_YC_MCHID));
                createInterfaceFeeOrder(orderPrinter, !isPDL ? YC_MCHID : PDL_YC_MCHID, fee, 0);

                orderPrinter.setMoneyReceived((long) (totalAmount));//实收
                orderPrinter.setInterfaceFee((long) fee);//接口费
                orderPrinter.setCommission((long) Math.round(totalAmount * 0.006f));//手续费
                orderPrinter.setAccount(orderPrinter.getMoneyReceived() - orderPrinter.getInterfaceFee() - orderPrinter.getCommission()); //到账
                orderPrinterService.updateOrderPrinter(orderPrinter);

                SplitBunch splitBunch = new SplitBunch("2", mchid, acctInfos);
                String jsonString = JSON.toJSONString(splitBunch);
                String encode = Base64.getEncoder().encodeToString(jsonString.getBytes());
                jsPay.setSplitBunch(encode);
            } else {
                orderPrinter.setMoneyReceived((long) totalAmount);
                orderPrinter.setCommission((long) Math.round(totalAmount * 0.006f));//手续费
                orderPrinter.setAccount(orderPrinter.getMoneyReceived() - orderPrinter.getCommission());
                orderPrinterService.updateOrderPrinter(orderPrinter);
            }
        }

        String myAgencyNo = MyConfig.AgencyNo_yc;
        String myPrivateKey = MyConfig.PrivateKey_yc;
        if (isPDL) {
            myAgencyNo = MyConfig.AgencyNo;
            myPrivateKey = MyConfig.PrivateKey;
        }

        String param = HttpsMain.format(jsPay, FunctionEnum.JS_PAY, myAgencyNo, myPrivateKey);
        log.info("请求报文{}", param);

        String response = HttpsMain.httpReq(MyConfig.PayUrl, param);
        log.info("响应报文{}", response);
        //验签
        if (!RsaUtil.verifyResponseSign(response)) {
            throw new Exception("验签失败");
        }

        JSONObject jsonObject = JSON.parseObject(response);

        String status = jsonObject.getString("status");
        String code = jsonObject.getString("code");
        if ("S".equals(status) && "0000".equals(code)) {
            //JSON.parseObject(response).getString("data"),取得返回结果的其中一个值 转化为Map
            Map<String, String> res = JSON.parseObject(jsonObject.getString("data"), new TypeReference<Map<String, String>>() {
            });
            Map<String, String> map = new HashMap<>();
            map.put("channelType", ChannelTypeEnum.WX.getCode());
            map.put("data", res.get("payInfo")); // 微信支付返回payInfo
            log.info("返回出去的数据{}", map);

            return JSON.toJSONString(map);
        } else {
            String msg = jsonObject.getString("msg");
            throw new Exception(msg);
        }
    }

    /**
     * 分割整数按比例分配
     */
    private int[] splitInt(int total, int[] ratios) {
        int length = ratios.length;
        int[] results = new int[length];

        // 计算总比例
        int sumOfRatios = Arrays.stream(ratios).sum();

        // 初步分配
        int allocatedSum = 0;
        for (int i = 0; i < length; i++) {
            results[i] = total * ratios[i] / sumOfRatios;
            allocatedSum += results[i];
        }

        // 分配余数
        int remainder = total - allocatedSum;
        for (int i = 0; remainder > 0; i = (i + 1) % length) {
            results[i]++;
            remainder--;
        }
        return results;
    }

    /**
     * 创建分账订单
     */
    private void createFZOrder(OrderPrinter orderPrinter, String mchId, int moneyReceived, int i) {
        List<SysUser> users = userService.query().eq("merchant_id", mchId).list();

        OrderPrinter fzOrder = new OrderPrinter();
        BeanUtils.copyProperties(orderPrinter, fzOrder);
        fzOrder.setOrderId(i + "_" + orderPrinter.getOrderId());
        fzOrder.setMchid(mchId);
        if (users.size() > 0)
            fzOrder.setUserId(users.get(0).getUserId());

        fzOrder.setMoneyReceived((long) moneyReceived);
        fzOrder.setCommission(0L);
        fzOrder.setInterfaceFee(0L);
        fzOrder.setAccount((long) moneyReceived);

        fzOrder.setDeviceName(fzOrder.getDeviceName() + "_分账订单");

        if (orderPrinterService.selectOrderPrinterByOrderId(fzOrder.getOrderId()) == null)
            orderPrinterService.insertOrderPrinter(fzOrder);
    }

    /**
     * 创建接口费订单
     */
    private void createInterfaceFeeOrder(OrderPrinter orderPrinter, String mchId, int interfaceFee, int i) {
        List<SysUser> users = userService.query().eq("merchant_id", mchId).list();

        OrderPrinter InterfaceFeeOrder = new OrderPrinter();
        BeanUtils.copyProperties(orderPrinter, InterfaceFeeOrder);
        InterfaceFeeOrder.setOrderId(i + "_" + orderPrinter.getOrderId());
        InterfaceFeeOrder.setMchid(mchId);
        if (users.size() > 0)
            InterfaceFeeOrder.setUserId(users.get(0).getUserId());

        InterfaceFeeOrder.setMoneyReceived(0L);
        InterfaceFeeOrder.setInterfaceFee((long) interfaceFee);
        InterfaceFeeOrder.setCommission(0L);
        InterfaceFeeOrder.setAccount((long) interfaceFee);

        InterfaceFeeOrder.setDeviceName(InterfaceFeeOrder.getDeviceName() + "_打印-手续费");

        if (orderPrinterService.selectOrderPrinterByOrderId(InterfaceFeeOrder.getOrderId()) == null)
            orderPrinterService.insertOrderPrinter(InterfaceFeeOrder);
    }

    /**
     * 支付回调通知
     */
    @ResponseBody
    @PostMapping("/notice")
    public String notice(HttpServletRequest request) throws Exception {
        BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream()));
        String tempLine = "";
        StringBuffer resultBuffer = new StringBuffer();
        while ((tempLine = reader.readLine()) != null) {
            resultBuffer.append(tempLine).append(System.getProperty("line.separator"));
        }
        String response = resultBuffer.toString();
        com.alibaba.fastjson2.JSONObject jsonObject = com.alibaba.fastjson2.JSONObject.parseObject(response);
        com.alibaba.fastjson2.JSONObject data = jsonObject.getJSONObject("data");
        System.out.println("回调报文：" + data);
        String orderId = data.getString("outTradeNo");
        String transactionId = data.getString("payChannelOrderNo");
        Date payTime = data.getDate("gmtPayment");

        OrderPrinter orderPrinter = orderPrinterService.selectOrderPrinterByOrderId(orderId);
        orderPrinter.setPayTime(payTime);
        orderPrinter.setTransactionId(transactionId);
        orderPrinter.setOrderStatus(1); // 已支付
        orderPrinter.setPayWay(1); // 微信支付

        orderPrinterService.updateOrderPrinter(orderPrinter);
        WebSocketServer.sendInfo("zfcg", orderPrinter.getDeviceId()); // 主推支付成功

        // 查询分账订单并更新状态
        OrderPrinter searchOrder = new OrderPrinter();
        searchOrder.setOrderId("_" + orderPrinter.getOrderId());
        List<OrderPrinter> _orders = orderPrinterService.selectOrderPrinterList(searchOrder);
        if (_orders.size() > 0) {//分账已支付
            for (OrderPrinter _order : _orders) {
                _order.setOrderStatus(1);
                _order.setTransactionId(transactionId);
                _order.setPayTime(payTime);
                orderPrinterService.updateOrderPrinter(_order);
            }
        }

        try {
            //汇总（主订单）
            OrderCollect collect = orderCollectService.query()
                .eq("merchant_id", orderPrinter.getMchid())
                .eq("time", DateUtils.parseDateToStr(DateUtils.YYYY_MM, orderPrinter.getCreateTime()))
                .one();

            if (collect == null) {
                collect = new OrderCollect();
                collect.setMerchantId(orderPrinter.getMchid());
                collect.setTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM, orderPrinter.getCreateTime()));
            }

            // 打印机订单统计到其他收入
            setCollectForPrinter(orderPrinter, collect);

            //如果存在分账订单
            if (_orders.size() > 0) {
                //（汇总 分账人）
                for (OrderPrinter _order : _orders) {
                    OrderCollect _collect = orderCollectService.query()
                        .eq("merchant_id", _order.getMchid())
                        .eq("time", DateUtils.parseDateToStr(DateUtils.YYYY_MM, _order.getCreateTime()))
                        .one();

                    if (_collect == null) {
                        _collect = new OrderCollect();
                        _collect.setMerchantId(_order.getMchid());
                        _collect.setTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM, _order.getCreateTime()));
                    }
                    setCollectForPrinter(_order, _collect);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        //更新设备表中的设备总收入
        DevicePrinter devicePrinter = devicePrinterService.selectDevicePrinterByDeviceId(orderPrinter.getDeviceId());
        if (devicePrinter != null) {
            Long currentPrice = devicePrinter.getCountPrice() != null ? devicePrinter.getCountPrice() : 0L;
            Long orderAmount = orderPrinter.getTotalAmount() != null ? orderPrinter.getTotalAmount().longValue() : 0L;
            devicePrinter.setCountPrice(currentPrice + orderAmount);
            devicePrinterService.updateDevicePrinter(devicePrinter);
        }

        if (!RsaUtil.verifyResponseSign(response)) {
            throw new Exception("验签失败");
        }
        return "SUCCESS";
    }

    /**
     * 设置打印机订单收入统计
     */
    private void setCollectForPrinter(OrderPrinter order, OrderCollect collect) {
        // 打印机订单统计到其他收入
        Long account = order.getAccount() != null ? order.getAccount() : 0L;
        collect.setOtherIncome(collect.getOtherIncome() + account);
        collect.setOtherOrderCount(collect.getOtherOrderCount() + 1);
        collect.setCount(collect.getCount() + account);

        if (collect.getId() != null) {
            orderCollectService.updateById(collect);
        } else {
            orderCollectService.save(collect);
        }
    }
}
