<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.order.mapper.OrderPrinterMapper">
    
    <resultMap type="OrderPrinter" id="OrderPrinterResult">
        <result property="orderId"    column="order_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="userId"    column="user_id"    />
        <result property="openid"    column="openid"    />
        <result property="phone"    column="phone"    />
        <result property="transactionId"    column="transaction_id"    />
        <result property="mchid"    column="mchid"    />
        <result property="appid"    column="appid"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="refundId"    column="refund_id"    />
        <result property="refundTime"    column="refund_time"    />
        <result property="payWay"    column="pay_way"    />
        <result property="voucherCode"    column="voucher_code"    />
        <result property="moneyReceived"    column="money_received"    />
        <result property="commission"    column="commission"    />
        <result property="interfaceFee"    column="interface_fee"    />
        <result property="account"    column="account"    />
        <result property="interfaceType"    column="interface_type"    />
        <result property="payTime"    column="pay_time"    />
        <result property="printTime"    column="print_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="roleId"    column="role_id"    />
        <result property="hide"    column="hide"    />
        <result property="status"    column="status"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="printStatus"    column="print_status_computed"    />
    </resultMap>

    <sql id="selectOrderPrinterVo">
        select order_id, device_id, device_name, user_id, openid, phone, transaction_id, mchid, appid, order_status,
        refund_id, refund_time, pay_way, voucher_code, money_received, commission, interface_fee, account,
        interface_type, pay_time, print_time, total_amount, create_by, create_time, update_by, update_time, remark, role_id, hide, status
        from order_printer
    </sql>

    <select id="selectOrderPrinterList" parameterType="OrderPrinter" resultMap="OrderPrinterResult">
        <include refid="selectOrderPrinterVo"/>
        <where>
            <choose>
                <when test="status != null">
                    and status = #{status}
                </when>
                <otherwise>
                    and status = 0
                </otherwise>
            </choose>
            <if test="deviceId != null  and deviceId != ''"> and device_id = #{deviceId}</if>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="orderStatus != null "> and order_status = #{orderStatus}</if>
            <if test="payWay != null "> and pay_way = #{payWay}</if>
            <if test="voucherCode != null  and voucherCode != ''"> and voucher_code = #{voucherCode}</if>
            <if test="startTime != null and endTime != null"> and create_time between #{startTime} and #{endTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectOrderPrinterByOrderId" parameterType="String" resultMap="OrderPrinterResult">
        <include refid="selectOrderPrinterVo"/>
        where order_id = #{orderId} and status = 0
    </select>
        
    <insert id="insertOrderPrinter" parameterType="OrderPrinter">
        insert into order_printer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="deviceName != null">device_name,</if>
            <if test="userId != null">user_id,</if>
            <if test="openid != null">openid,</if>
            <if test="phone != null">phone,</if>
            <if test="transactionId != null">transaction_id,</if>
            <if test="mchid != null">mchid,</if>
            <if test="appid != null">appid,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="refundId != null">refund_id,</if>
            <if test="refundTime != null">refund_time,</if>
            <if test="payWay != null">pay_way,</if>
            <if test="voucherCode != null">voucher_code,</if>
            <if test="moneyReceived != null">money_received,</if>
            <if test="commission != null">commission,</if>
            <if test="interfaceFee != null">interface_fee,</if>
            <if test="account != null">account,</if>
            <if test="interfaceType != null">interface_type,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="printTime != null">print_time,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="roleId != null">role_id,</if>
            <if test="hide != null">hide,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="deviceName != null">#{deviceName},</if>
            <if test="userId != null">#{userId},</if>
            <if test="openid != null">#{openid},</if>
            <if test="phone != null">#{phone},</if>
            <if test="transactionId != null">#{transactionId},</if>
            <if test="mchid != null">#{mchid},</if>
            <if test="appid != null">#{appid},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="refundId != null">#{refundId},</if>
            <if test="refundTime != null">#{refundTime},</if>
            <if test="payWay != null">#{payWay},</if>
            <if test="voucherCode != null">#{voucherCode},</if>
            <if test="moneyReceived != null">#{moneyReceived},</if>
            <if test="commission != null">#{commission},</if>
            <if test="interfaceFee != null">#{interfaceFee},</if>
            <if test="account != null">#{account},</if>
            <if test="interfaceType != null">#{interfaceType},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="printTime != null">#{printTime},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="roleId != null">#{roleId},</if>
            <if test="hide != null">#{hide},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateOrderPrinter" parameterType="OrderPrinter">
        update order_printer
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="openid != null">openid = #{openid},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="transactionId != null">transaction_id = #{transactionId},</if>
            <if test="mchid != null">mchid = #{mchid},</if>
            <if test="appid != null">appid = #{appid},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="refundId != null">refund_id = #{refundId},</if>
            <if test="refundTime != null">refund_time = #{refundTime},</if>
            <if test="payWay != null">pay_way = #{payWay},</if>
            <if test="voucherCode != null">voucher_code = #{voucherCode},</if>
            <if test="moneyReceived != null">money_received = #{moneyReceived},</if>
            <if test="commission != null">commission = #{commission},</if>
            <if test="interfaceFee != null">interface_fee = #{interfaceFee},</if>
            <if test="account != null">account = #{account},</if>
            <if test="interfaceType != null">interface_type = #{interfaceType},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="printTime != null">print_time = #{printTime},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="roleId != null">role_id = #{roleId},</if>
            <if test="hide != null">hide = #{hide},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where order_id = #{orderId} and (status = 0 or #{status} is not null)
    </update>

    <delete id="deleteOrderPrinterByOrderId" parameterType="String">
        delete from order_printer where order_id = #{orderId}
    </delete>

    <delete id="deleteOrderPrinterByOrderIds" parameterType="String">
        delete from order_printer where order_id in
        <foreach item="orderId" collection="array" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </delete>

    <!-- 查询用户订单列表（包含总金额） -->
    <select id="selectOrderPrinterListWithTotalAmount" parameterType="String" resultMap="OrderPrinterResult">
        SELECT
            o.order_id, o.device_id, o.device_name, o.user_id, o.openid, o.phone,
            o.transaction_id, o.mchid, o.appid, o.order_status, o.refund_id, o.refund_time,
            o.pay_way, o.voucher_code, o.money_received, o.commission, o.interface_fee,
            o.account, o.interface_type, o.pay_time, o.print_time, o.create_by,
            o.create_time, o.update_by, o.update_time, o.remark, o.role_id, o.hide, o.status,
            COALESCE(SUM(t.task_price), 0) as total_amount
        FROM order_printer o
        LEFT JOIN order_printer_tasks t ON o.order_id = t.order_id AND t.status = 0
        WHERE o.openid = #{openid} AND o.status = 0
        GROUP BY o.order_id, o.device_id, o.device_name, o.user_id, o.openid, o.phone,
                 o.transaction_id, o.mchid, o.appid, o.order_status, o.refund_id, o.refund_time,
                 o.pay_way, o.voucher_code, o.money_received, o.commission, o.interface_fee,
                 o.account, o.interface_type, o.pay_time, o.print_time, o.create_by,
                 o.create_time, o.update_by, o.update_time, o.remark, o.role_id, o.hide, o.status
        ORDER BY o.create_time DESC
    </select>

    <!-- 批量计算订单总金额 -->
    <select id="batchCalculateOrderTotalAmount" resultType="java.util.Map">
        SELECT
            t.order_id as orderId,
            COALESCE(SUM(t.task_price), 0) as totalAmount
        FROM order_printer_tasks t
        WHERE t.status = 0 AND t.order_id IN
        <foreach item="orderId" collection="orderIds" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        GROUP BY t.order_id
    </select>

    <!-- 查询用户订单列表（支持状态筛选） -->
    <select id="selectOrderPrinterListWithStatusFilter" resultMap="OrderPrinterResult">
        SELECT
            o.order_id, o.device_id, o.device_name, o.user_id, o.openid, o.phone,
            o.transaction_id, o.mchid, o.appid, o.order_status, o.refund_id, o.refund_time,
            o.pay_way, o.voucher_code, o.money_received, o.commission, o.interface_fee,
            o.account, o.interface_type, o.pay_time, o.print_time, o.create_by,
            o.create_time, o.update_by, o.update_time, o.remark, o.role_id, o.hide,
            COALESCE(SUM(t.task_price), 0) as total_amount,
            CASE
                WHEN COUNT(t.task_id) = 0 THEN 'pending'
                WHEN COUNT(CASE WHEN t.print_status IN (0, 1, 3) THEN 1 END) > 0 THEN 'pending'
                ELSE 'completed'
            END as print_status_computed
        FROM order_printer o
        LEFT JOIN order_printer_tasks t ON o.order_id = t.order_id AND t.status = 0
        WHERE o.openid = #{openid} AND o.status = 0
        <if test="payStatus != null">
            AND o.order_status = #{payStatus}
        </if>
        GROUP BY o.order_id, o.device_id, o.device_name, o.user_id, o.openid, o.phone,
                 o.transaction_id, o.mchid, o.appid, o.order_status, o.refund_id, o.refund_time,
                 o.pay_way, o.voucher_code, o.money_received, o.commission, o.interface_fee,
                 o.account, o.interface_type, o.pay_time, o.print_time, o.create_by,
                 o.create_time, o.update_by, o.update_time, o.remark, o.role_id, o.hide, o.status
        <if test="printStatus != null">
            HAVING print_status_computed = #{printStatus}
        </if>
        ORDER BY o.create_time DESC
    </select>
</mapper>