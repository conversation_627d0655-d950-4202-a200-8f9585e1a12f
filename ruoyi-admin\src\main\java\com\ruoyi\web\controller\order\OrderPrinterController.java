package com.ruoyi.web.controller.order;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;


import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;

import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.order.domain.OrderPrinter;
import com.ruoyi.order.domain.OrderPrinterTask;
import com.ruoyi.order.service.IOrderPrinterService;
import com.ruoyi.order.service.IOrderPrinterBusinessService;
import com.yunchuang.wxapp.model.domain.WxappLoginUser;
import com.yunchuang.wxapp.service.WxAppAuthService;
import com.yunchuang.wxapp.util.UserContext;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;



/**
 * 订单打印机控制器
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@RestController
@RequestMapping("/order/printer")
public class OrderPrinterController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(OrderPrinterController.class);

    @Autowired
    private IOrderPrinterService orderPrinterService;

    @Autowired
    private IOrderPrinterBusinessService orderPrinterBusinessService;

    @Autowired
    private WxAppAuthService wxAppAuthService;





    /**
     * 查询订单打印机列表
     */
    @PreAuthorize("@ss.hasPermi('order:printer:list')")
//    @Anonymous
    @GetMapping("/list")
    public TableDataInfo list(OrderPrinter orderPrinter)
    {
        startPage();
        List<OrderPrinter> list = orderPrinterService.selectOrderPrinterList(orderPrinter);
        return getDataTable(list);
    }

    /**
     * 导出订单打印机列表
     */
    @PreAuthorize("@ss.hasPermi('order:printer:export')")
    @Log(title = "订单打印机", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OrderPrinter orderPrinter)
    {
        List<OrderPrinter> list = orderPrinterService.selectOrderPrinterList(orderPrinter);
        ExcelUtil<OrderPrinter> util = new ExcelUtil<OrderPrinter>(OrderPrinter.class);
        util.exportExcel(response, list, "订单打印机数据");
    }

    /**
     * 获取订单打印机详细信息
     */
    @PreAuthorize("@ss.hasPermi('order:printer:query')")
    @GetMapping(value = "/{orderId}")
    public AjaxResult getInfo(@PathVariable("orderId") String orderId)
    {
        return success(orderPrinterService.selectOrderPrinterByOrderId(orderId));
    }

    /**
     * 新增订单打印机
     */
    @PreAuthorize("@ss.hasPermi('order:printer:add')")
    @Log(title = "订单打印机", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OrderPrinter orderPrinter)
    {
        return toAjax(orderPrinterService.insertOrderPrinter(orderPrinter));
    }

    /**
     * 修改订单打印机
     */
    @PreAuthorize("@ss.hasPermi('order:printer:edit')")
    @Log(title = "订单打印机", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OrderPrinter orderPrinter)
    {
        return toAjax(orderPrinterService.updateOrderPrinter(orderPrinter));
    }

    /**
     * 删除订单打印机
     */
    @PreAuthorize("@ss.hasPermi('order:printer:remove')")
    @Log(title = "订单打印机", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    public AjaxResult remove(@PathVariable String[] orderIds)
    {
        return toAjax(orderPrinterService.deleteOrderPrinterByOrderIds(orderIds));
    }
    
   
    
    /**
     * 支付回调处理
     */
    @PostMapping("/payNotify")
    public AjaxResult payNotify(@RequestBody Map<String, Object> params) {
        // 校验必要参数
        String orderId = (String) params.get("orderId");
        String transactionId = (String) params.get("transactionId");
        
        if (StringUtils.isEmpty(orderId)) {
            return AjaxResult.error("订单ID不能为空");
        }
        
        try {
            // 处理支付成功
            boolean result = orderPrinterService.paySuccess(orderId, transactionId);
            if (result) {
                return AjaxResult.success("支付处理成功");
            } else {
                return AjaxResult.error("支付处理失败，订单不存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("支付处理失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询订单打印任务
     */
    @GetMapping("/tasks/{orderId}")
    public AjaxResult getOrderTasks(@PathVariable("orderId") String orderId) {
        List<OrderPrinterTask> tasks = orderPrinterService.getOrderTasks(orderId);
        return AjaxResult.success(tasks);
    }
    
    /**
     * 更新打印任务状态
     */
    @PostMapping("/task/status")
    public AjaxResult updateTaskStatus(@RequestBody Map<String, Object> params) {
        // 校验必要参数
        String taskId = (String) params.get("taskId");
        Integer status = (Integer) params.get("status");
        String errorMsg = (String) params.get("errorMsg");
        
        if (StringUtils.isEmpty(taskId) || status == null) {
            return AjaxResult.error("任务ID和状态不能为空");
        }
        
        try {
            // 更新任务状态
            int result = orderPrinterService.updateTaskStatus(taskId, status, errorMsg);
            if (result > 0) {
                return AjaxResult.success("更新状态成功");
            } else {
                return AjaxResult.error("更新状态失败，任务不存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("更新状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询用户订单列表
     * @param request HTTP请求
     * @param payStatus 支付状态筛选：1-已支付，3-已退款，不传查询所有
     * @param printStatus 打印状态筛选：pending-待打印，completed-已完成，不传查询所有
     */
    @GetMapping("/user")
    public AjaxResult getUserOrders(
            HttpServletRequest request,
            @RequestParam(value = "payStatus", required = false) Integer payStatus,
            @RequestParam(value = "printStatus", required = false) String printStatus) {
        // 从token中获取openid
        String openid = getCurrentUserOpenid(request);
        if (StringUtils.isEmpty(openid)) {
            return AjaxResult.error("用户未登录或openid获取失败");
        }

        log.info("查询用户订单列表，openid: {}, payStatus: {}, printStatus: {}", openid, payStatus, printStatus);

        // 使用优化后的方法，支持状态筛选
        List<OrderPrinter> orders = orderPrinterService.selectOrderPrinterListWithStatusFilter(openid, payStatus, printStatus);

        log.info("查询到{}个订单", orders != null ? orders.size() : 0);

        return AjaxResult.success(orders);
    }
    
    /**
     * 获取用户订单详情（包含打印任务）
     */
    @Anonymous
    @GetMapping("/detail/{orderId}")
    public AjaxResult getOrderDetail(@PathVariable("orderId") String orderId, @RequestParam(value = "openid", required = false) String openid) {
        if (StringUtils.isEmpty(orderId)) {
            return AjaxResult.error("订单ID不能为空");
        }
        
        // 获取订单信息
        OrderPrinter order = orderPrinterService.selectOrderPrinterByOrderId(orderId);
        if (order == null) {
            return AjaxResult.error("订单不存在");
        }
        
        // 如果提供了openid，验证订单是否属于该用户
        if (StringUtils.isNotEmpty(openid) && !openid.equals(order.getOpenid())) {
            return AjaxResult.error("无权查看该订单");
        }
        
        // 获取订单关联的打印任务
        List<OrderPrinterTask> tasks = orderPrinterService.getOrderTasks(orderId);

        // 计算订单总金额
        Double totalAmount = orderPrinterService.calculateOrderTotalAmount(orderId);
        order.setTotalAmount(totalAmount);

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("order", order);
        result.put("tasks", tasks);
        result.put("totalAmount", totalAmount); // 额外返回总金额字段

        return AjaxResult.success(result);
    }
    
    /**
     * 创建订单接口（第一步：创建订单）
     */
    @Log(title = "创建打印订单", businessType = BusinessType.INSERT)
    @PostMapping("/createOrder")
    public AjaxResult createOrder(
            HttpServletRequest request,
            @RequestParam("deviceId") String deviceId,
            @RequestParam(value = "deviceName", required = false) String deviceName,
            @RequestParam(value = "openid", required = false) String openid,
            @RequestParam(value = "phone", required = false) String phone) {

        // 记录请求日志
        Map<String, Object> params = new HashMap<>();
        params.put("deviceId", deviceId);
        params.put("deviceName", deviceName);
        params.put("openid", openid);
        params.put("phone", phone);
        orderPrinterBusinessService.logRequestInfo(request, "创建订单", params);

        // 参数验证
        if (StringUtils.isEmpty(deviceId)) {
            log.error("参数验证失败: 设备ID不能为空");
            return AjaxResult.error("设备ID不能为空");
        }

        try {
            // 调用业务服务创建订单
            Map<String, Object> result = orderPrinterBusinessService.createOrder(deviceId, deviceName, openid, phone);
            log.info("创建订单成功，订单ID: {}", result.get("orderId"));

            return AjaxResult.success("创建订单成功", result);
        } catch (Exception e) {
            log.error("创建订单失败: {}", e.getMessage(), e);
            return AjaxResult.error("创建订单失败: " + e.getMessage());
        }
    }

    /**
     * 上传文件并计算价格接口（第二步：上传文件）
     */
    @Log(title = "上传文件并计算价格", businessType = BusinessType.INSERT)
    @PostMapping("/uploadFile")
    public AjaxResult uploadFile(
            HttpServletRequest request,
            @RequestParam("orderId") String orderId,
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "copies", required = false, defaultValue = "1") Integer copies,
            @RequestParam(value = "colorMode", required = false, defaultValue = "0") Integer colorMode,
            @RequestParam(value = "duplexMode", required = false, defaultValue = "0") Integer duplexMode,
            @RequestParam(value = "paperType", required = false, defaultValue = "1") Integer paperType,
            @RequestParam(value = "pageRange", required = false) String pageRange) {

        // 记录请求日志
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", orderId);
        params.put("copies", copies);
        params.put("colorMode", colorMode);
        params.put("duplexMode", duplexMode);
        params.put("paperType", paperType);
        params.put("pageRange", pageRange);
        orderPrinterBusinessService.logRequestInfo(request, "上传文件并计算价格", params);

        // 记录文件信息
        orderPrinterBusinessService.logFileInfo(file, null);

        try {
            // 调用业务服务上传文件
            Map<String, Object> result = orderPrinterBusinessService.uploadFile(
                    orderId, file, copies, colorMode, duplexMode, paperType, pageRange);
            log.info("上传文件并计算价格成功，任务ID: {}, 价格: {}", result.get("taskId"), result.get("taskPrice"));

            return AjaxResult.success("上传文件成功", result);
        } catch (Exception e) {
            log.error("上传文件失败: {}", e.getMessage(), e);
            return AjaxResult.error("上传文件失败: " + e.getMessage());
        }
    }

    /**
     * 从请求中获取当前用户的openid
     * 优先级：1. 从ThreadLocal中获取 2. 从JWT token中获取 3. 返回null
     */
    private String getCurrentUserOpenid(HttpServletRequest request) {
        try {
            // 方法1：尝试从ThreadLocal中获取（如果经过了认证拦截器）
            WxappLoginUser currentUser = UserContext.getCurrentUser();
            if (currentUser != null && StringUtils.isNotEmpty(currentUser.getOpenid())) {
                log.info("从ThreadLocal获取到openid: {}", currentUser.getOpenid());
                return currentUser.getOpenid();
            }

            // 方法2：尝试从JWT token中获取
            WxappLoginUser loginUser = wxAppAuthService.getClientUser(request);
            if (loginUser != null && StringUtils.isNotEmpty(loginUser.getOpenid())) {
                log.info("从JWT token获取到openid: {}", loginUser.getOpenid());
                return loginUser.getOpenid();
            }
        } catch (Exception e) {
            log.warn("获取用户openid失败: {}", e.getMessage());
        }
        return null;
    }


}