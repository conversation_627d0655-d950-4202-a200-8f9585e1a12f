package com.ruoyi.order.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 上传文件请求对象
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
public class UploadFileRequest {

    /** 订单ID（前端固定生成） */
    @NotBlank(message = "订单ID不能为空")
    private String orderId;

    /** 设备ID */
    @NotBlank(message = "设备ID不能为空")
    private String deviceId;

    /** 设备名称 */
    private String deviceName;

    /** 用户openid */
    private String openid;

    /** 用户手机号 */
    private String phone;

    /** 打印份数 */
    @NotNull(message = "打印份数不能为空")
    private Integer copies = 1;

    /** 颜色模式 0-黑白 1-彩色 */
    @NotNull(message = "颜色模式不能为空")
    private Integer colorMode = 0;

    /** 双面模式 0-单面 1-双面 */
    @NotNull(message = "双面模式不能为空")
    private Integer duplexMode = 0;

    /** 纸张类型 1-A4 2-A5 3-照片纸 */
    @NotNull(message = "纸张类型不能为空")
    private Integer paperType = 1;

    /** 页码范围 */
    private String pageRange;

    /** 是否是最后一次上传 */
    @NotNull(message = "是否最后一次上传标识不能为空")
    private Boolean isLastUpload = false;
}
