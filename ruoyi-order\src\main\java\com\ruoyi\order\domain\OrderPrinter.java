package com.ruoyi.order.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 订单打印机对象 order_printer
 * 
 * <AUTHOR>
 * @date 2024-06-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("order_printer")
public class OrderPrinter extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 订单id */
    @Excel(name = "订单id")
    @TableId(value = "order_id", type = IdType.INPUT)
    private String orderId;

    /** 打印机设备id */
    @Excel(name = "打印机设备id")
    private String deviceId;

    /** 打印机名称 */
    @Excel(name = "打印机名称")
    private String deviceName;

    /** 用户id */
    private Long userId;

    /** 微信用户openid */
    private String openid;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phone;

    /** 三方订单id */
    private String transactionId;

    /** 商户id */
    private String mchid;

    /** 小程序appid */
    private String appid;

    /** 订单状态 0未支付 1已支付 2已取消 3已退款 4已打印 5打印中 6打印失败 */
    @Excel(name = "订单状态")
    private Integer orderStatus;

    private String refundId;
    private Date refundTime;

    /** 支付方式 1微信 2支付宝 3现金 */
    @Excel(name = "支付方式")
    private Integer payWay;

    /** 优惠券码 */
    @Excel(name = "优惠券码")
    private String voucherCode;

    /** 实收（被分账的金额） */
    @Excel(name = "实收")
    private Long moneyReceived;

    /** 手续费 */
    @Excel(name = "手续费")
    private Long commission;

    /** 接口费 */
    @Excel(name = "接口费")
    private Long interfaceFee;

    /** 到账（实收 - 手续费 - 接口费） */
    @Excel(name = "到账")
    private Long account;

    /** 技术接口类型 */
    @Excel(name = "技术接口类型")
    private Integer interfaceType;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date payTime;

    /** 打印时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "打印时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date printTime;

    /** 代理商id */
    private Long roleId;

    /** 是否隐藏 0-否 1-是 */
    private Integer hide;

    /** 订单状态（0-正常 1-删除） */
    @Excel(name = "订单状态")
    private Integer status;

    /** 非数据库字段 */
    @TableField(exist = false)
    private Date startTime;

    @TableField(exist = false)
    private Date endTime;

    /** 订单总金额（所有任务价格之和）单位：分 */
    @Excel(name = "订单总金额(分)")
    private Long totalAmount;

    /** 打印状态（基于任务状态计算）- 非数据库字段 */
    @TableField(exist = false)
    private String printStatus; // pending-待打印, completed-已完成
}