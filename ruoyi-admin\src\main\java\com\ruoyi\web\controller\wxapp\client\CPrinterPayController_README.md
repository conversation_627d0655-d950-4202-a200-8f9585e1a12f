# 打印机支付控制器 (CPrinterPayController)

## 概述
这个控制器模拟了 `HLPay.java` 的支付功能，但专门针对打印机订单。它只支持微信支付，简化了支付流程。

## 主要功能

### 1. 获取支付页面 (`/getOpenId`)
- **请求方式**: GET
- **路径**: `/client/wxapp/printer/pay/getOpenId`
- **参数**:
  - `attach`: 订单ID
  - `userInfo`: 用户信息JSON字符串，包含openid
- **功能**: 根据订单信息和用户openid，返回支付页面或重定向到外部支付页面

### 2. 发起微信支付 (`/jsPay`)
- **请求方式**: POST
- **路径**: `/client/wxapp/printer/pay/jsPay`
- **参数**:
  - `totalAmount`: 支付金额（分）
  - `openId`: 微信用户openid
  - `outTradeNo`: 订单号
  - `mchid`: 商户号
- **功能**: 创建微信支付订单，处理分账逻辑，返回支付参数

### 3. 支付回调 (`/notice`)
- **请求方式**: POST
- **路径**: `/client/wxapp/printer/pay/notice`
- **功能**: 处理支付成功回调，更新订单状态，统计收入数据

## 与原HLPay的主要区别

1. **只支持微信支付**: 移除了支付宝相关代码
2. **针对打印机订单**: 使用 `OrderPrinter` 和 `DevicePrinter` 实体
3. **简化的参数**: 移除了userId参数，只使用openId
4. **打印机特定逻辑**: 
   - 支付描述改为"打印付款"
   - 产品描述为"打印服务"
   - 收入统计到"其他收入"类别

## 分账逻辑
支持设备级别的分账配置，通过 `DevicePrinter.subAccount` 字段配置分账比例。

## 手续费处理
- 支持接口费配置（当前代码中暂时禁用）
- 默认手续费率为0.6%
- 支持云创和潘朵拉两套配置

## 数据统计
- 更新设备总收入 (`DevicePrinter.countPrice`)
- 更新月度收入统计 (`OrderCollect`)
- 支持分账订单的独立统计

## 使用示例

### 前端调用支付接口
```javascript
// 1. 先获取支付页面
window.location.href = '/client/wxapp/printer/pay/getOpenId?attach=订单ID&userInfo=' + 
  encodeURIComponent(JSON.stringify({openid: 'wx_openid'}));

// 2. 在支付页面调用jsPay接口
fetch('/client/wxapp/printer/pay/jsPay', {
  method: 'POST',
  headers: {'Content-Type': 'application/x-www-form-urlencoded'},
  body: 'totalAmount=100&openId=wx_openid&outTradeNo=订单ID&mchid=商户号'
})
.then(response => response.json())
.then(data => {
  // 使用返回的支付参数调用微信支付
  wx.requestPayment(JSON.parse(data.data));
});
```

## 注意事项

1. **订单状态**: 确保订单状态为0（未支付）才能发起支付
2. **设备绑定**: 订单必须关联有效的打印机设备
3. **用户权限**: 支持不同部门的用户使用不同的支付配置
4. **回调验签**: 支付回调会进行签名验证
5. **WebSocket通知**: 支付成功后会通过WebSocket推送消息到设备

## 错误处理

常见错误信息：
- "订单号错误": 订单不存在
- "用户绑定错误": 用户信息异常
- "订单状态错误": 订单已支付或状态异常
- "设备绑定异常": 设备不存在
- "缺少微信openId": openId为空
- "验签失败": 支付回调验签失败
