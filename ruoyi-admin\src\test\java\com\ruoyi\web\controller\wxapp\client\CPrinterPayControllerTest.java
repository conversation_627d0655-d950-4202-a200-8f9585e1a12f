package com.ruoyi.web.controller.wxapp.client;

import com.ruoyi.device.domain.DevicePrinter;
import com.ruoyi.device.service.IDevicePrinterService;
import com.ruoyi.order.domain.OrderPrinter;
import com.ruoyi.order.service.IOrderPrinterService;
import com.ruoyi.system.service.ISysUserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 打印机支付控制器测试类
 */
public class CPrinterPayControllerTest {

    @Mock
    private IOrderPrinterService orderPrinterService;

    @Mock
    private ISysUserService userService;

    @Mock
    private IDevicePrinterService devicePrinterService;

    @InjectMocks
    private CPrinterPayController cPrinterPayController;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(cPrinterPayController).build();
    }

    @Test
    void testGetOpenId_WithValidParams() throws Exception {
        // 准备测试数据
        OrderPrinter orderPrinter = new OrderPrinter();
        orderPrinter.setOrderId("test_order_123");
        orderPrinter.setDeviceId("device_001");
        orderPrinter.setUserId(1L);
        orderPrinter.setMchid("test_mchid");
        orderPrinter.setTotalAmount(10.0);

        when(orderPrinterService.selectOrderPrinterByOrderId(anyString()))
                .thenReturn(orderPrinter);

        // 执行测试
        mockMvc.perform(get("/client/wxapp/printer/pay/getOpenId")
                .param("attach", "test_order_123")
                .param("userInfo", "{\"openid\":\"test_openid\"}"))
                .andExpect(status().isOk());
    }

    @Test
    void testGetOpenId_WithMissingOpenId() throws Exception {
        // 测试缺少openid的情况
        mockMvc.perform(get("/client/wxapp/printer/pay/getOpenId")
                .param("attach", "test_order_123")
                .param("userInfo", "{}"))
                .andExpect(status().isOk())
                .andExpect(result -> {
                    // 应该返回null
                    assert result.getResponse().getContentAsString().isEmpty();
                });
    }

    @Test
    void testDoJsPay_WithMissingOpenId() throws Exception {
        // 准备测试数据
        OrderPrinter orderPrinter = new OrderPrinter();
        orderPrinter.setOrderId("test_order_123");
        orderPrinter.setOrderStatus(0); // 未支付状态
        orderPrinter.setUserId(1L);

        when(orderPrinterService.selectOrderPrinterByOrderId(anyString()))
                .thenReturn(orderPrinter);

        // 测试缺少openId的情况
        mockMvc.perform(post("/client/wxapp/printer/pay/jsPay")
                .param("totalAmount", "100")
                .param("openId", "")
                .param("outTradeNo", "test_order_123")
                .param("mchid", "test_mchid"))
                .andExpect(status().isOk())
                .andExpect(content().string("缺少微信openId"));
    }

    @Test
    void testDoJsPay_WithInvalidOrderStatus() throws Exception {
        // 准备测试数据
        OrderPrinter orderPrinter = new OrderPrinter();
        orderPrinter.setOrderId("test_order_123");
        orderPrinter.setOrderStatus(1); // 已支付状态
        orderPrinter.setUserId(1L);

        when(orderPrinterService.selectOrderPrinterByOrderId(anyString()))
                .thenReturn(orderPrinter);

        // 测试订单状态错误的情况
        mockMvc.perform(post("/client/wxapp/printer/pay/jsPay")
                .param("totalAmount", "100")
                .param("openId", "test_openid")
                .param("outTradeNo", "test_order_123")
                .param("mchid", "test_mchid"))
                .andExpect(status().isOk())
                .andExpect(content().string("订单状态错误1"));
    }

    @Test
    void testDoJsPay_WithNonExistentOrder() throws Exception {
        // 测试订单不存在的情况
        when(orderPrinterService.selectOrderPrinterByOrderId(anyString()))
                .thenReturn(null);

        mockMvc.perform(post("/client/wxapp/printer/pay/jsPay")
                .param("totalAmount", "100")
                .param("openId", "test_openid")
                .param("outTradeNo", "non_existent_order")
                .param("mchid", "test_mchid"))
                .andExpect(status().isOk())
                .andExpect(content().string("订单号错误"));
    }
}
