<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.device.mapper.DevicePrinterMapper">
    
    <resultMap type="DevicePrinter" id="DevicePrinterResult">
        <id property="deviceId" column="device_id"/>
        <result property="deviceName" column="device_name"/>
        <result property="deviceCode" column="device_code"/>
        <result property="deviceModel" column="device_model"/>
        <result property="brandId" column="brand_id"/>
        <result property="softwareId" column="software_id"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="district" column="district"/>
        <result property="detailAddress" column="detail_address"/>
        <result property="deviceStatus" column="device_status"/>
        <result property="onlineStatus" column="online_status"/>
        <result property="lng" column="lng"/>
        <result property="lat" column="lat"/>
        <result property="agentId" column="agent_id"/>
        <result property="agentName" column="agent_name"/>
        <result property="managerId" column="manager_id"/>
        <result property="managerName" column="manager_name"/>
        <result property="printerType" column="printer_type"/>
        <result property="paperType" column="paper_type"/>
        <result property="colorSupport" column="color_support"/>
        <result property="duplexSupport" column="duplex_support"/>
        <result property="tonerLevel" column="toner_level"/>
        <result property="paperRemain" column="paper_remain"/>
        <result property="lastHeartbeat" column="last_heartbeat"/>
        <result property="totalPrints" column="total_prints"/>
        <result property="monthPrints" column="month_prints"/>
        <result property="errorCode" column="error_code"/>
        <result property="errorMessage" column="error_message"/>
        <result property="pricePerPage" column="price_per_page"/>
        <result property="countPrice" column="count_price"/>
        <result property="subAccount" column="sub_account"/>
        <result property="isWarning" column="is_warning"/>
        <result property="warningThreshold" column="warning_threshold"/>
        <result property="warningRecipients" column="warning_recipients"/>
        <result property="tags" column="tags" typeHandler="com.ruoyi.common.handler.JsonListTypeHandler"/>
        <result property="images" column="images" typeHandler="com.ruoyi.common.handler.JsonListTypeHandler"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectDevicePrinterVo">
        select device_id, device_name, device_code, device_model, brand_id, software_id, province, city, district, detail_address,
        device_status, online_status, lng, lat, agent_id, agent_name, manager_id, manager_name, printer_type, paper_type,
        color_support, duplex_support, toner_level, paper_remain, last_heartbeat, total_prints, month_prints, error_code,
        error_message, price_per_page, count_price, total_amount, sub_account, is_warning, warning_threshold, warning_recipients,
        tags, images, create_by, create_time, update_by, update_time, remark, del_flag
        from device_printer
    </sql>

    <select id="selectDevicePrinterList" parameterType="DevicePrinter" resultMap="DevicePrinterResult">
        <include refid="selectDevicePrinterVo"/>
        <where>  
            <if test="deviceId != null  and deviceId != ''"> and device_id = #{deviceId}</if>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="deviceCode != null  and deviceCode != ''"> and device_code = #{deviceCode}</if>
            <if test="deviceModel != null  and deviceModel != ''"> and device_model like concat('%', #{deviceModel}, '%')</if>
            <if test="brandId != null "> and brand_id = #{brandId}</if>
            <if test="softwareId != null "> and software_id = #{softwareId}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="district != null  and district != ''"> and district = #{district}</if>
            <if test="deviceStatus != null "> and device_status = #{deviceStatus}</if>
            <if test="onlineStatus != null "> and online_status = #{onlineStatus}</if>
            <if test="agentId != null "> and agent_id = #{agentId}</if>
            <if test="managerId != null "> and manager_id = #{managerId}</if>
            <if test="printerType != null "> and printer_type = #{printerType}</if>
            <if test="paperType != null "> and paper_type = #{paperType}</if>
            <if test="colorSupport != null "> and color_support = #{colorSupport}</if>
            <if test="duplexSupport != null "> and duplex_support = #{duplexSupport}</if>
            <if test="isWarning != null "> and is_warning = #{isWarning}</if>
            and del_flag = 0
        </where>
    </select>
    
    <select id="selectDevicePrinterByDeviceId" parameterType="String" resultMap="DevicePrinterResult">
        <include refid="selectDevicePrinterVo"/>
        where device_id = #{deviceId} and del_flag = 0
    </select>
        
    <insert id="insertDevicePrinter" parameterType="DevicePrinter">
        insert into device_printer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">device_id,</if>
            <if test="deviceName != null">device_name,</if>
            <if test="deviceCode != null">device_code,</if>
            <if test="deviceModel != null">device_model,</if>
            <if test="brandId != null">brand_id,</if>
            <if test="softwareId != null">software_id,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="district != null">district,</if>
            <if test="detailAddress != null">detail_address,</if>
            <if test="deviceStatus != null">device_status,</if>
            <if test="onlineStatus != null">online_status,</if>
            <if test="lng != null">lng,</if>
            <if test="lat != null">lat,</if>
            <if test="agentId != null">agent_id,</if>
            <if test="agentName != null">agent_name,</if>
            <if test="managerId != null">manager_id,</if>
            <if test="managerName != null">manager_name,</if>
            <if test="printerType != null">printer_type,</if>
            <if test="paperType != null">paper_type,</if>
            <if test="colorSupport != null">color_support,</if>
            <if test="duplexSupport != null">duplex_support,</if>
            <if test="tonerLevel != null">toner_level,</if>
            <if test="paperRemain != null">paper_remain,</if>
            <if test="lastHeartbeat != null">last_heartbeat,</if>
            <if test="totalPrints != null">total_prints,</if>
            <if test="monthPrints != null">month_prints,</if>
            <if test="errorCode != null">error_code,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="pricePerPage != null">price_per_page,</if>
            <if test="countPrice != null">count_price,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="subAccount != null">sub_account,</if>
            <if test="isWarning != null">is_warning,</if>
            <if test="warningThreshold != null">warning_threshold,</if>
            <if test="warningRecipients != null">warning_recipients,</if>
            <if test="tags != null">tags,</if>
            <if test="images != null">images,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deviceId != null">#{deviceId},</if>
            <if test="deviceName != null">#{deviceName},</if>
            <if test="deviceCode != null">#{deviceCode},</if>
            <if test="deviceModel != null">#{deviceModel},</if>
            <if test="brandId != null">#{brandId},</if>
            <if test="softwareId != null">#{softwareId},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="district != null">#{district},</if>
            <if test="detailAddress != null">#{detailAddress},</if>
            <if test="deviceStatus != null">#{deviceStatus},</if>
            <if test="onlineStatus != null">#{onlineStatus},</if>
            <if test="lng != null">#{lng},</if>
            <if test="lat != null">#{lat},</if>
            <if test="agentId != null">#{agentId},</if>
            <if test="agentName != null">#{agentName},</if>
            <if test="managerId != null">#{managerId},</if>
            <if test="managerName != null">#{managerName},</if>
            <if test="printerType != null">#{printerType},</if>
            <if test="paperType != null">#{paperType},</if>
            <if test="colorSupport != null">#{colorSupport},</if>
            <if test="duplexSupport != null">#{duplexSupport},</if>
            <if test="tonerLevel != null">#{tonerLevel},</if>
            <if test="paperRemain != null">#{paperRemain},</if>
            <if test="lastHeartbeat != null">#{lastHeartbeat},</if>
            <if test="totalPrints != null">#{totalPrints},</if>
            <if test="monthPrints != null">#{monthPrints},</if>
            <if test="errorCode != null">#{errorCode},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="pricePerPage != null">#{pricePerPage},</if>
            <if test="countPrice != null">#{countPrice},</if>
            <if test="subAccount != null">#{subAccount},</if>
            <if test="isWarning != null">#{isWarning},</if>
            <if test="warningThreshold != null">#{warningThreshold},</if>
            <if test="warningRecipients != null">#{warningRecipients},</if>
            <if test="tags != null">#{tags,typeHandler=com.ruoyi.common.handler.JsonListTypeHandler},</if>
            <if test="images != null">#{images,typeHandler=com.ruoyi.common.handler.JsonListTypeHandler},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
        </trim>
    </insert>

    <update id="updateDevicePrinter" parameterType="DevicePrinter">
        update device_printer
        <trim prefix="SET" suffixOverrides=",">
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="deviceCode != null">device_code = #{deviceCode},</if>
            <if test="deviceModel != null">device_model = #{deviceModel},</if>
            <if test="brandId != null">brand_id = #{brandId},</if>
            <if test="softwareId != null">software_id = #{softwareId},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="district != null">district = #{district},</if>
            <if test="detailAddress != null">detail_address = #{detailAddress},</if>
            <if test="deviceStatus != null">device_status = #{deviceStatus},</if>
            <if test="onlineStatus != null">online_status = #{onlineStatus},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="agentName != null">agent_name = #{agentName},</if>
            <if test="managerId != null">manager_id = #{managerId},</if>
            <if test="managerName != null">manager_name = #{managerName},</if>
            <if test="printerType != null">printer_type = #{printerType},</if>
            <if test="paperType != null">paper_type = #{paperType},</if>
            <if test="colorSupport != null">color_support = #{colorSupport},</if>
            <if test="duplexSupport != null">duplex_support = #{duplexSupport},</if>
            <if test="tonerLevel != null">toner_level = #{tonerLevel},</if>
            <if test="paperRemain != null">paper_remain = #{paperRemain},</if>
            <if test="lastHeartbeat != null">last_heartbeat = #{lastHeartbeat},</if>
            <if test="totalPrints != null">total_prints = #{totalPrints},</if>
            <if test="monthPrints != null">month_prints = #{monthPrints},</if>
            <if test="errorCode != null">error_code = #{errorCode},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="pricePerPage != null">price_per_page = #{pricePerPage},</if>
            <if test="countPrice != null">count_price = #{countPrice},</if>
            <if test="subAccount != null">sub_account = #{subAccount},</if>
            <if test="isWarning != null">is_warning = #{isWarning},</if>
            <if test="warningThreshold != null">warning_threshold = #{warningThreshold},</if>
            <if test="warningRecipients != null">warning_recipients = #{warningRecipients},</if>
            <if test="tags != null">tags = #{tags,typeHandler=com.ruoyi.common.handler.JsonListTypeHandler},</if>
            <if test="images != null">images = #{images,typeHandler=com.ruoyi.common.handler.JsonListTypeHandler},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where device_id = #{deviceId}
    </update>

    <delete id="deleteDevicePrinterByDeviceId" parameterType="String">
        update device_printer set del_flag = 1 where device_id = #{deviceId}
    </delete>

    <delete id="deleteDevicePrinterByDeviceIds" parameterType="String">
        update device_printer set del_flag = 1 where device_id in 
        <foreach item="deviceId" collection="array" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
    </delete>
    
    <update id="updateDeviceOnlineStatus">
        update device_printer set online_status = #{onlineStatus}, last_heartbeat = sysdate() where device_id = #{deviceId}
    </update>
    
    <update id="updateDeviceConsumables" parameterType="DevicePrinter">
        update device_printer
        <trim prefix="SET" suffixOverrides=",">
            <if test="tonerLevel != null">toner_level = #{tonerLevel},</if>
            <if test="paperRemain != null">paper_remain = #{paperRemain},</if>
            <if test="errorCode != null">error_code = #{errorCode},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            last_heartbeat = sysdate()
        </trim>
        where device_id = #{deviceId}
    </update>
</mapper> 